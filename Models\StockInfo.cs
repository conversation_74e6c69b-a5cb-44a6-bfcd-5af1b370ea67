using System;

namespace StockMonitor.Models
{
    public class StockInfo
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public decimal CurrentPrice { get; set; }
        public decimal Change { get; set; }
        public decimal ChangePercent { get; set; }
        public decimal High { get; set; }
        public decimal Low { get; set; }
        public decimal Yesterday { get; set; }
        public DateTime LastUpdate { get; set; } = DateTime.Now;

        public bool IsUp => Change >= 0;
        
        public string ChangeText => Change >= 0 ? $"+{Change:F2}" : $"{Change:F2}";
        public string ChangePercentText => ChangePercent >= 0 ? $"+{ChangePercent:F2}%" : $"{ChangePercent:F2}%";
    }

    public class StockSearchResult
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Market { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        
        public string DisplayText => $"{Code} - {Name}";
    }
}
