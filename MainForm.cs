using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using StockMonitor.Controls;
using StockMonitor.Models;
using StockMonitor.Services;

namespace StockMonitor
{
    public partial class MainForm : Form
    {
        private StockSearchControl _searchControl;
        private StockDataGridView _dataGridView;
        private Timer _refreshTimer;
        private StatusStrip _statusStrip;
        private ToolStripStatusLabel _statusLabel;
        private ToolStripStatusLabel _countLabel;
        private ToolStripStatusLabel _timeLabel;

        public MainForm()
        {
            InitializeComponent();
            InitializeTimer();
        }

        private void InitializeComponent()
        {
            this.Text = "股票监控系统";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(600, 400);

            // 创建搜索控件
            _searchControl = new StockSearchControl
            {
                Location = new Point(10, 10),
                Size = new Size(400, 35),
                Anchor = AnchorStyles.Top | AnchorStyles.Left
            };
            _searchControl.StockSelected += OnStockSelected;
            this.Controls.Add(_searchControl);

            // 创建数据表格
            _dataGridView = new StockDataGridView
            {
                Location = new Point(10, 55),
                Size = new Size(760, 480),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };
            this.Controls.Add(_dataGridView);

            // 创建状态栏
            CreateStatusBar();

            // 添加一些示例股票
            AddSampleStocks();
        }

        private void CreateStatusBar()
        {
            _statusStrip = new StatusStrip();
            
            _statusLabel = new ToolStripStatusLabel("就绪")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            _countLabel = new ToolStripStatusLabel("股票数量: 0")
            {
                AutoSize = true
            };
            
            _timeLabel = new ToolStripStatusLabel("最后更新: --:--:--")
            {
                AutoSize = true
            };
            
            _statusStrip.Items.AddRange(new ToolStripItem[] { _statusLabel, _countLabel, _timeLabel });
            this.Controls.Add(_statusStrip);
        }

        private void InitializeTimer()
        {
            _refreshTimer = new Timer
            {
                Interval = 1000, // 1秒刷新一次
                Enabled = true
            };
            _refreshTimer.Tick += OnRefreshTimer;
        }

        private async void OnStockSelected(StockSearchResult result)
        {
            try
            {
                _statusLabel.Text = $"正在获取 {result.Code} 的数据...";
                
                var stockInfo = await StockApiService.Instance.GetStockInfoAsync(result.Code);
                if (stockInfo != null)
                {
                    stockInfo.Name = result.Name; // 使用搜索结果中的名称
                    _dataGridView.AddStock(stockInfo);
                    UpdateStatusBar();
                    _statusLabel.Text = $"已添加股票: {result.Code} - {result.Name}";
                }
                else
                {
                    _statusLabel.Text = $"获取股票 {result.Code} 数据失败";
                    MessageBox.Show($"无法获取股票 {result.Code} 的数据，请稍后重试。", 
                        "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                _statusLabel.Text = "添加股票失败";
                MessageBox.Show($"添加股票失败: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void OnRefreshTimer(object? sender, EventArgs e)
        {
            if (_dataGridView.StockData.Any())
            {
                try
                {
                    var codes = _dataGridView.StockData.Select(s => s.Code).ToList();
                    var updatedStocks = await StockApiService.Instance.GetStockInfosAsync(codes);
                    
                    // 保持原有的名称
                    foreach (var updatedStock in updatedStocks)
                    {
                        var originalStock = _dataGridView.StockData.FirstOrDefault(s => s.Code == updatedStock.Code);
                        if (originalStock != null)
                        {
                            updatedStock.Name = originalStock.Name;
                        }
                    }
                    
                    _dataGridView.UpdateStockData(updatedStocks);
                    UpdateStatusBar();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"刷新数据失败: {ex.Message}");
                }
            }
        }

        private void UpdateStatusBar()
        {
            _countLabel.Text = $"股票数量: {_dataGridView.StockData.Count}";
            _timeLabel.Text = $"最后更新: {DateTime.Now:HH:mm:ss}";
            
            if (_dataGridView.StockData.Any())
            {
                var upCount = _dataGridView.StockData.Count(s => s.Change > 0);
                var downCount = _dataGridView.StockData.Count(s => s.Change < 0);
                var flatCount = _dataGridView.StockData.Count(s => s.Change == 0);
                
                _statusLabel.Text = $"涨: {upCount} | 跌: {downCount} | 平: {flatCount}";
            }
            else
            {
                _statusLabel.Text = "就绪";
            }
        }

        private async void AddSampleStocks()
        {
            // 添加一些示例股票
            var sampleCodes = new[] { "SH000001", "SZ399001", "SH000300" };
            var sampleNames = new[] { "上证指数", "深证成指", "沪深300" };
            
            for (int i = 0; i < sampleCodes.Length; i++)
            {
                try
                {
                    var stockInfo = await StockApiService.Instance.GetStockInfoAsync(sampleCodes[i]);
                    if (stockInfo != null)
                    {
                        stockInfo.Name = sampleNames[i];
                        _dataGridView.AddStock(stockInfo);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"添加示例股票失败: {ex.Message}");
                }
            }
            
            UpdateStatusBar();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
            base.OnFormClosing(e);
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            
            if (_dataGridView != null)
            {
                _dataGridView.Size = new Size(
                    this.ClientSize.Width - 20,
                    this.ClientSize.Height - 100
                );
            }
        }
    }
}
