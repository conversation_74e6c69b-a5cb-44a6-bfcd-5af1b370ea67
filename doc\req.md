

## 本仓库代码约定 
- 在代码文件中不使用中文
- 一般情况下不需要写注释; 只在关键技术或操作时才写注释

## 主界面截图
![alt text](PixPin_2025-06-13_11-27-16.png)

## 功能描述

### 搜索框

- 按 https://github.com/zhangxiangliang/stock-api 仓库里的请求接口, 实现在搜索框输入数据时调用 `搜索股票代码` 接口
- 接口返回结果, 以列表形式显示在输入框下方(类似google搜索输入关键字后自动联想和补全效果)
- 列表中每一行右侧显示 + 按钮,点击后,该股票数据添加到表格中

### 表格

- 按照1hz刷新行情数据
- 右键有"删除"菜单,点击后删除该股票
- 左键按住拖动可以调整该行位置


## 技术要求

- 采用C#语言实现
- 支持 windows10 自带的 .net 运行时(即不额外安装运行时)