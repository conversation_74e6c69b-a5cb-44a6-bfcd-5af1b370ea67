@echo off
echo Building Stock Monitor Application...
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET SDK is not installed or not in PATH
    echo Please install .NET SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

REM Clean previous build
echo Cleaning previous build...
if exist bin rmdir /s /q bin
if exist obj rmdir /s /q obj

REM Restore packages
echo Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo Error: Failed to restore packages
    pause
    exit /b 1
)

REM Build the application
echo Building application...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo To run the application:
echo   dotnet run
echo.
echo Or run the executable from:
echo   bin\Release\net48\StockMonitor.exe
echo.
pause
