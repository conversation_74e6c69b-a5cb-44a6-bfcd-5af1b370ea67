using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using StockMonitor.Models;
using StockMonitor.Services;

namespace StockMonitor.Controls
{
    public partial class StockSearchControl : UserControl
    {
        private TextBox _searchTextBox;
        private Panel _resultsPanel;
        private List<StockSearchResult> _searchResults = new List<StockSearchResult>();
        private bool _isSearching = false;

        public event Action<StockSearchResult>? StockSelected;

        public StockSearchControl()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(400, 35);
            this.BackColor = Color.White;
            this.BorderStyle = BorderStyle.FixedSingle;

            // 搜索文本框
            _searchTextBox = new TextBox
            {
                Location = new Point(5, 8),
                Size = new Size(390, 20),
                Font = new Font("Microsoft YaHei", 9F),
                BorderStyle = BorderStyle.None,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };
            _searchTextBox.TextChanged += OnSearchTextChanged;
            _searchTextBox.KeyDown += OnSearchTextKeyDown;
            _searchTextBox.Leave += OnSearchTextLeave;
            this.Controls.Add(_searchTextBox);

            // 搜索结果面板
            _resultsPanel = new Panel
            {
                Location = new Point(0, 35),
                Size = new Size(400, 0),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Visible = false,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };
            this.Controls.Add(_resultsPanel);

            // 设置占位符文本
            SetPlaceholderText();
        }

        private void SetPlaceholderText()
        {
            if (string.IsNullOrEmpty(_searchTextBox.Text))
            {
                _searchTextBox.Text = "输入股票代码或名称搜索...";
                _searchTextBox.ForeColor = Color.Gray;
            }
        }

        private void OnSearchTextChanged(object? sender, EventArgs e)
        {
            if (_searchTextBox.ForeColor == Color.Gray)
            {
                _searchTextBox.Text = "";
                _searchTextBox.ForeColor = Color.Black;
            }

            var text = _searchTextBox.Text.Trim();
            if (text.Length >= 2 && !_isSearching)
            {
                _ = PerformSearchAsync(text);
            }
            else if (text.Length < 2)
            {
                HideResults();
            }
        }

        private void OnSearchTextKeyDown(object? sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                HideResults();
                _searchTextBox.Text = "";
                SetPlaceholderText();
            }
        }

        private void OnSearchTextLeave(object? sender, EventArgs e)
        {
            // 延迟隐藏，允许用户点击结果
            Task.Delay(200).ContinueWith(_ => 
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() => 
                    {
                        if (!_resultsPanel.ContainsFocus)
                        {
                            HideResults();
                        }
                    }));
                }
            });

            if (string.IsNullOrEmpty(_searchTextBox.Text))
            {
                SetPlaceholderText();
            }
        }

        private async Task PerformSearchAsync(string keyword)
        {
            _isSearching = true;
            
            try
            {
                var results = await StockApiService.Instance.SearchStocksAsync(keyword);
                _searchResults = results;
                
                if (results.Any())
                {
                    ShowResults(results);
                }
                else
                {
                    HideResults();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"搜索失败: {ex.Message}");
                HideResults();
            }
            finally
            {
                _isSearching = false;
            }
        }

        private void ShowResults(List<StockSearchResult> results)
        {
            _resultsPanel.Controls.Clear();
            
            int itemHeight = 30;
            int maxItems = Math.Min(results.Count, 8); // 最多显示8个结果
            
            for (int i = 0; i < maxItems; i++)
            {
                var result = results[i];
                var itemPanel = CreateResultItem(result, i * itemHeight);
                _resultsPanel.Controls.Add(itemPanel);
            }
            
            _resultsPanel.Size = new Size(400, maxItems * itemHeight);
            _resultsPanel.Visible = true;
            _resultsPanel.BringToFront();
        }

        private Panel CreateResultItem(StockSearchResult result, int top)
        {
            var itemPanel = new Panel
            {
                Location = new Point(0, top),
                Size = new Size(398, 30),
                BackColor = Color.White,
                Cursor = Cursors.Hand
            };

            var nameLabel = new Label
            {
                Text = result.DisplayText,
                Location = new Point(5, 8),
                Size = new Size(320, 15),
                Font = new Font("Microsoft YaHei", 9F),
                ForeColor = Color.Black
            };
            itemPanel.Controls.Add(nameLabel);

            var addButton = new Button
            {
                Text = "+",
                Location = new Point(360, 5),
                Size = new Size(25, 20),
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold),
                ForeColor = Color.Green,
                BackColor = Color.LightGreen,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            addButton.FlatAppearance.BorderSize = 1;
            addButton.FlatAppearance.BorderColor = Color.Green;
            addButton.Click += (s, e) => OnStockSelected(result);
            itemPanel.Controls.Add(addButton);

            // 鼠标悬停效果
            itemPanel.MouseEnter += (s, e) => itemPanel.BackColor = Color.LightBlue;
            itemPanel.MouseLeave += (s, e) => itemPanel.BackColor = Color.White;
            nameLabel.MouseEnter += (s, e) => itemPanel.BackColor = Color.LightBlue;
            nameLabel.MouseLeave += (s, e) => itemPanel.BackColor = Color.White;

            return itemPanel;
        }

        private void OnStockSelected(StockSearchResult result)
        {
            StockSelected?.Invoke(result);
            HideResults();
            _searchTextBox.Text = "";
            SetPlaceholderText();
        }

        private void HideResults()
        {
            _resultsPanel.Visible = false;
            _resultsPanel.Size = new Size(400, 0);
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            if (_searchTextBox != null)
            {
                _searchTextBox.Size = new Size(this.Width - 10, 20);
            }
            if (_resultsPanel != null)
            {
                _resultsPanel.Size = new Size(this.Width, _resultsPanel.Height);
            }
        }
    }
}
