@echo off
echo Starting Stock Monitor Application...
echo.

REM Check if application is built
if not exist "bin\Release\net48\StockMonitor.exe" (
    echo Application not found. Building first...
    call build.bat
    if %errorlevel% neq 0 (
        echo Build failed. Cannot start application.
        pause
        exit /b 1
    )
)

REM Run the application
echo Starting application...
start "" "bin\Release\net48\StockMonitor.exe"

echo Application started successfully!
timeout /t 2 >nul
