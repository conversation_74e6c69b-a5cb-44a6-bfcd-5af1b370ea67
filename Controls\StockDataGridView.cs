using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using StockMonitor.Models;

namespace StockMonitor.Controls
{
    public class StockDataGridView : DataGridView
    {
        private List<StockInfo> _stockData = new List<StockInfo>();
        private ContextMenuStrip _contextMenu;
        private int _dragRowIndex = -1;

        public List<StockInfo> StockData => _stockData;

        public StockDataGridView()
        {
            InitializeComponent();
            InitializeContextMenu();
        }

        private void InitializeComponent()
        {
            this.AllowUserToAddRows = false;
            this.AllowUserToDeleteRows = false;
            this.AllowUserToResizeRows = false;
            this.ReadOnly = true;
            this.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.MultiSelect = false;
            this.RowHeadersVisible = false;
            this.BackgroundColor = Color.White;
            this.BorderStyle = BorderStyle.Fixed3D;
            this.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            this.GridColor = Color.LightGray;
            this.Font = new Font("Microsoft YaHei", 9F);
            this.RowTemplate.Height = 25;
            this.AllowDrop = true;

            // 添加列
            AddColumns();

            // 事件处理
            this.CellFormatting += OnCellFormatting;
            this.MouseDown += OnMouseDown;
            this.DragOver += OnDragOver;
            this.DragDrop += OnDragDrop;
        }

        private void AddColumns()
        {
            this.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Code",
                HeaderText = "代码",
                Width = 80,
                ReadOnly = true
            });

            this.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "名称",
                Width = 120,
                ReadOnly = true
            });

            this.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentPrice",
                HeaderText = "现价",
                Width = 80,
                ReadOnly = true,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            this.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Change",
                HeaderText = "涨跌",
                Width = 80,
                ReadOnly = true,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            this.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ChangePercent",
                HeaderText = "涨跌幅",
                Width = 80,
                ReadOnly = true,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            this.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "High",
                HeaderText = "最高",
                Width = 80,
                ReadOnly = true,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            this.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Low",
                HeaderText = "最低",
                Width = 80,
                ReadOnly = true,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            this.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastUpdate",
                HeaderText = "更新时间",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
        }

        private void InitializeContextMenu()
        {
            _contextMenu = new ContextMenuStrip();
            
            var deleteItem = new ToolStripMenuItem("删除", null, OnDeleteStock);
            deleteItem.Image = SystemIcons.Error.ToBitmap();
            _contextMenu.Items.Add(deleteItem);

            var moveUpItem = new ToolStripMenuItem("上移", null, OnMoveUp);
            _contextMenu.Items.Add(moveUpItem);

            var moveDownItem = new ToolStripMenuItem("下移", null, OnMoveDown);
            _contextMenu.Items.Add(moveDownItem);

            this.ContextMenuStrip = _contextMenu;
        }

        public void AddStock(StockInfo stock)
        {
            if (_stockData.Any(s => s.Code == stock.Code))
            {
                MessageBox.Show($"股票 {stock.Code} 已存在！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            _stockData.Add(stock);
            RefreshData();
        }

        public void UpdateStockData(List<StockInfo> newData)
        {
            foreach (var newStock in newData)
            {
                var existingStock = _stockData.FirstOrDefault(s => s.Code == newStock.Code);
                if (existingStock != null)
                {
                    var index = _stockData.IndexOf(existingStock);
                    _stockData[index] = newStock;
                }
            }
            RefreshData();
        }

        public void RefreshData()
        {
            this.Rows.Clear();
            
            foreach (var stock in _stockData)
            {
                var row = new DataGridViewRow();
                row.CreateCells(this);
                
                row.Cells[0].Value = stock.Code;
                row.Cells[1].Value = stock.Name;
                row.Cells[2].Value = stock.CurrentPrice.ToString("F2");
                row.Cells[3].Value = stock.ChangeText;
                row.Cells[4].Value = stock.ChangePercentText;
                row.Cells[5].Value = stock.High.ToString("F2");
                row.Cells[6].Value = stock.Low.ToString("F2");
                row.Cells[7].Value = stock.LastUpdate.ToString("HH:mm:ss");
                
                this.Rows.Add(row);
            }
        }

        private void OnCellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex >= 0 && e.RowIndex < _stockData.Count)
            {
                var stock = _stockData[e.RowIndex];
                
                // 根据涨跌设置颜色
                if (e.ColumnIndex == 2 || e.ColumnIndex == 3 || e.ColumnIndex == 4) // 现价、涨跌、涨跌幅
                {
                    if (stock.IsUp)
                    {
                        e.CellStyle.ForeColor = Color.Red;
                    }
                    else if (stock.Change < 0)
                    {
                        e.CellStyle.ForeColor = Color.Green;
                    }
                    else
                    {
                        e.CellStyle.ForeColor = Color.Black;
                    }
                }
            }
        }

        private void OnDeleteStock(object? sender, EventArgs e)
        {
            if (this.SelectedRows.Count > 0)
            {
                var rowIndex = this.SelectedRows[0].Index;
                if (rowIndex >= 0 && rowIndex < _stockData.Count)
                {
                    var stock = _stockData[rowIndex];
                    var result = MessageBox.Show($"确定要删除股票 {stock.Code} - {stock.Name} 吗？", 
                        "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        _stockData.RemoveAt(rowIndex);
                        RefreshData();
                    }
                }
            }
        }

        private void OnMoveUp(object? sender, EventArgs e)
        {
            if (this.SelectedRows.Count > 0)
            {
                var rowIndex = this.SelectedRows[0].Index;
                if (rowIndex > 0)
                {
                    var stock = _stockData[rowIndex];
                    _stockData.RemoveAt(rowIndex);
                    _stockData.Insert(rowIndex - 1, stock);
                    RefreshData();
                    this.Rows[rowIndex - 1].Selected = true;
                }
            }
        }

        private void OnMoveDown(object? sender, EventArgs e)
        {
            if (this.SelectedRows.Count > 0)
            {
                var rowIndex = this.SelectedRows[0].Index;
                if (rowIndex < _stockData.Count - 1)
                {
                    var stock = _stockData[rowIndex];
                    _stockData.RemoveAt(rowIndex);
                    _stockData.Insert(rowIndex + 1, stock);
                    RefreshData();
                    this.Rows[rowIndex + 1].Selected = true;
                }
            }
        }

        private void OnMouseDown(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                var hitTest = this.HitTest(e.X, e.Y);
                if (hitTest.RowIndex >= 0)
                {
                    _dragRowIndex = hitTest.RowIndex;
                    this.DoDragDrop(this.Rows[hitTest.RowIndex], DragDropEffects.Move);
                }
            }
        }

        private void OnDragOver(object? sender, DragEventArgs e)
        {
            e.Effect = DragDropEffects.Move;
        }

        private void OnDragDrop(object? sender, DragEventArgs e)
        {
            var clientPoint = this.PointToClient(new Point(e.X, e.Y));
            var hitTest = this.HitTest(clientPoint.X, clientPoint.Y);
            
            if (hitTest.RowIndex >= 0 && _dragRowIndex >= 0 && hitTest.RowIndex != _dragRowIndex)
            {
                var stock = _stockData[_dragRowIndex];
                _stockData.RemoveAt(_dragRowIndex);
                _stockData.Insert(hitTest.RowIndex, stock);
                RefreshData();
                this.Rows[hitTest.RowIndex].Selected = true;
            }
            
            _dragRowIndex = -1;
        }
    }
}
